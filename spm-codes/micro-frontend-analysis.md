# Micro Frontend Architecture Analysis - SPM Codes

## Overview
The SPM Codes application implements a hybrid micro frontend architecture using two primary patterns:
1. **Postmate-based iframe communication** for sophisticated micro frontends
2. **Simple iframe embedding** for external applications and third-party integrations

## Architecture Patterns

### 1. Postmate-Based Micro Frontends
Uses the Postmate library for secure parent-child iframe communication with bidirectional messaging.

#### Key Components:
- **OnboardingMicroApp.vue** - New user onboarding flow
- **EmailMicroApps.vue** - Email management interface
- **EmailBuilderMicroApp.vue** - Email template builder
- **WorkflowBuilder.vue** - Automation workflow builder
- **MediaFilesApp.vue** - Media center management
- **MembershipBuilder.vue** - Membership site builder
- **LocationUserUpdateMicroApp.vue** - User profile updates

### 2. Simple Iframe Embedding
Direct iframe embedding for external applications and third-party services.

#### Key Components:
- **MarketplaceFrame.vue** - GoHighLevel marketplace
- **Ideas.vue** - Ideas portal
- **SaasFasttrack.vue** - SaaS fast track program
- **TipaltiIframe.vue** - Payment processing
- **CustomMenuLinkPage.vue** - Custom menu links
- **ChatWidgetSettings.vue** - Chat widget preview

## Micro Frontend Registry

### Postmate-Based Applications

| Application | Component | URL Configuration | Authentication | Data Types |
|-------------|-----------|------------------|----------------|------------|
| **Onboarding** | OnboardingMicroApp.vue | `config.newOnboardingFrontend + route.path` | authKey, Firebase token | User data, company info, location data |
| **Email Home** | EmailMicroApps.vue | `config.emailHomeAppUrl + route.path` | Firebase token, API key | User ID, email, location data, timezone |
| **Email Builder** | Builder.vue | `config.emailPreviewAppUrl + route.path` | Firebase token, API key | User permissions, location data, template data |
| **Workflow Builder** | WorkflowBuilder.vue | `config.workflowAppURL + route.fullPath` | Firebase token, API key | Location ID, user data, company plan |
| **Media Center** | MediaFilesApp.vue | `config.mediaCenterAppUrl` | Firebase token, API key | User ID, company ID, upload settings |
| **Membership Builder** | MembershipBuilder.vue | `membershipUrl + route.fullPath` | Firebase token, cookie | Location ID, user type, company ID |
| **User Update** | LocationUserUpdateMicroApp.vue | `config.newOnboardingFrontend + route.path` | User data object | User profile data, company info |

### Simple Iframe Applications

| Application | Component | URL Configuration | Authentication | Data Types |
|-------------|-----------|------------------|----------------|------------|
| **Marketplace** | MarketplaceFrame.vue | `https://mp.gohighlevel.com?marketplace=${companyId}` | Company ID in URL | Company identification |
| **Ideas Portal** | Ideas.vue | `https://ideas.gohighlevel.com` | None (static URL) | None |
| **SaaS FastTrack** | SaasFasttrack.vue | Dynamic URL from config | Company-based | Company identification |
| **Tipalti Payment** | TipaltiIframe.vue | Dynamic with HMAC signature | HMAC authentication | Promoter ID, timestamp, payer info |
| **Custom Menu Links** | CustomMenuLinkPage.vue | User-defined URLs | Variable | Custom parameters |
| **Chat Widget** | ChatWidgetSettings.vue | Internal preview URL | API-based | Widget configuration |

## Configuration Management

### Environment-Based URLs
All micro frontend URLs are configured in `src/config/index.ts` with environment-specific values:

```typescript
// Development URLs
emailHomeAppUrl: 'http://localhost:3003'
emailPreviewAppUrl: 'http://localhost:3001'
mediaCenterAppUrl: 'http://localhost:4040'
workflowAppURL: 'http://localhost:3002'
newOnboardingFrontend: 'https://penitent-circle-mayur.surge.sh'

// Staging URLs
emailHomeAppUrl: 'https://hl-email-home.web.app'
emailPreviewAppUrl: 'https://hl-email-preview.web.app'
mediaCenterAppUrl: 'https://hl-media-center.firebaseapp.com'
workflowAppURL: 'https://hl-workflow.web.app'

// Production URLs
emailHomeAppUrl: 'https://email-home-prod.web.app'
emailPreviewAppUrl: 'https://email-preview-prod.web.app'
mediaCenterAppUrl: 'https://media-center-prod.firebaseapp.com'
workflowAppURL: 'https://workflow.msgsndr.com'
```

## Authentication Mechanisms

### 1. Firebase Token-Based Authentication
Most Postmate applications use Firebase ID tokens for authentication:
```javascript
async getToken() {
  return await firebase.auth().currentUser.getIdToken()
}
```

### 2. API Key Authentication
Location-specific API keys passed to micro frontends:
```javascript
apiKey: locationData.api_key
```

### 3. HMAC Signature Authentication
Used for Tipalti payment integration:
```javascript
let hashkey = crypto.createHmac('sha256', config.tipalti.masterKey)
  .update(encodeURI(queryStr))
  .digest('hex')
```

### 4. Company ID-Based Authentication
Simple company identification for marketplace and other services:
```javascript
baseURL + `?marketplace=${companyId}`
```

## Inter-Frame Communication

### Postmate Communication Patterns
Bidirectional communication using Postmate events:

#### Parent to Child:
- Route changes: `child.call('routeChange', { path, direction })`
- Token updates: `child.call('updated-token', token)`

#### Child to Parent:
- Navigation: `child.on('spm-ts', data => router.push(data.router))`
- Analytics: `child.on('ga-event', data => trackGaPageView(data))`
- Data updates: `child.on('user-data', data => updateData())`
- Close events: `child.on('close', () => router.push(...))`

### PostMessage Communication
Used for simple iframe communication:
```javascript
window.addEventListener('message', (e) => {
  if(e.data.handle_checkout) {
    // Handle checkout data from funnel iframe
  }
})
```

## Routing Integration

### Route Definitions
Micro frontends are integrated into the main routing system in `src/routes/pmd.ts` and `src/routes/v2.ts`:

```javascript
{
  path: '/onboarding',
  name: 'onboarding_micro_app',
  component: OnboardingMicroApp,
},
{
  path: '/location/:location_id/emails',
  name: 'emails-v2',
  component: EmailMicroApps,
}
```

### External Redirects
Some routes redirect to external URLs:
```javascript
{
  path: '/university',
  name: 'university',
  meta: {
    RedirectExternalUrl: 'https://university.gohighlevel.com?loginCode=UP$D6e',
  },
}
```

## Loading Mechanisms

### 1. Dynamic URL Construction
URLs are dynamically constructed based on:
- Current route path
- Environment configuration
- User/company context
- Authentication tokens

### 2. Container-Based Loading
Micro frontends are loaded into specific DOM containers:
```javascript
container: this.$refs.emailBuilder as HTMLElement
```

### 3. Conditional Loading
Some micro frontends load conditionally based on:
- User permissions
- Company status
- Feature flags
- Route parameters

## Security Considerations

### 1. Token Refresh Mechanism
Automatic token refresh for expired Firebase tokens:
```javascript
child.on('refresh-token', async () => {
  child.call('updated-token', await this.getToken())
})
```

### 2. Iframe Sandboxing
Some iframes use sandbox attributes for security:
```html
<iframe sandbox="allow-scripts" srcdoc="content">
```

### 3. CORS and Domain Restrictions
Micro frontends are hosted on trusted domains and use proper CORS configurations.

## State Management

### Vuex Integration
Iframe state is managed through Vuex store (`src/store/iframe.ts`):
```javascript
state: { handshake: {} }
mutations: {
  updateHandshake(state, handshake) {
    state.handshake = handshake
  }
}
```

### Data Synchronization
Parent application synchronizes data with micro frontends through:
- Initial model data
- Event-based updates
- Token refresh mechanisms
- Route change notifications

## Implementation Details

### Postmate Configuration Examples

#### Onboarding Micro App
```javascript
this.handshake = new Postmate({
  container: this.$refs.onboardingV2 as HTMLElement,
  url: `${this.newOnboardingFrontend}${this.$route.path}`,
  name: 'onboarding-v2-home',
  classListArray: ['onboarding-iframe'],
  model: {
    websiteSignup: this.user?.token ? true : false,
    firstName: this.user?.firstName,
    authKey: this.authKey,
    isPasswordPending: this.user?.isPasswordPending,
    locationId: locationId,
    snapshotId: this.company.onboardingInfo.snapshotId,
    snapshotName: snapshotName,
    snapshotType: snapshotType,
    isAgencyUser: this.user?.type == User.TYPE_AGENCY
  },
})
```

#### Email Builder Micro App
```javascript
this.handshake = new Postmate({
  container: this.$refs.emailBuilder as HTMLElement,
  url: `${config.emailHomeAppUrl}${this.$route.path.replace('/v2/location/','/location/')}`,
  name: 'emails-home',
  model: {
    userId,
    email,
    apiKey: locationData.api_key,
    tokenId: await this.getToken(),
    companyId: locationData.company_id,
    timeZone: locationData.timezone,
    userName: `${first_name} ${last_name}`,
  },
})
```

#### Workflow Builder Micro App
```javascript
const handshake = new Postmate({
  container: this.$refs.workflowBuilder,
  url: `${workflowURL}${this.$route.fullPath}`,
  name: 'workflow-builder',
  model: {
    locationId: location.id,
    internalUser: this.$store.state.user?.internalUser || false,
    tokenId: tokenId,
    apiKey: location.apiKey,
    authApiKey: auth.apiKey,
    timeZone: this.location.timezone,
    smtpWarningFlag: this.showSmtpWarning,
    company: {
      plan: this.$store.state.company?.company?.stripe_active_plan,
      id: this.$store.state.company?.company?.id,
    },
    user,
    users: this.$store.state.users.users
  },
})
```

### Simple Iframe Examples

#### Marketplace Frame
```javascript
computed: {
  marketplaceURL() {
    let baseURL = 'https://mp.gohighlevel.com'
    if(this.pathExtension) baseURL = `${baseURL}/${this.pathExtension}`
    const companyId = this.$store.state?.company?.company?.id
    baseURL = baseURL + `?marketplace=${companyId}`
    return baseURL
  }
}
```

#### Tipalti Payment Integration
```javascript
computed: {
  iframeRequestURL() {
    let currentTimestamp = Math.round(+ new Date / 1000)
    let query = {
      idap: this.promoterId,
      ts: currentTimestamp,
      payer: this.payer,
    }

    const queryStr = Object.keys(query).map(key => key + '=' + query[key]).join('&');
    let hashkey = crypto.createHmac('sha256', config.tipalti.masterKey)
      .update(encodeURI(queryStr))
      .digest('hex');

    let baseURL = config.tipalti.baseURL
    let url = `${baseURL}${this.pageName}?${queryStr}&hashkey=${hashkey}`
    return url
  }
}
```

## Custom Menu Link Integration

The application supports custom menu links that can be opened in iframes or new tabs:

```javascript
customMenuLinkClicked(link) {
  switch (link.open_mode) {
    case 'iframe':
      this.$router.push({
        name: 'location_custom_menu_link',
        params: { location_id: this.currentLocationId, id: link.id },
      })
      break
    case 'new_tab':
      this.redirect(link.url)
      break
  }
}
```

## Error Handling and Loading States

### Loading States
Most micro frontends implement loading states:
```javascript
data() {
  return {
    loading: true,
    childLoaded: false,
    errorMsg: ''
  }
}
```

### Error Handling
Error handling for iframe communication:
```javascript
window.addEventListener('message', (e) => {
  try {
    if(e.data.handle_checkout) {
      // Handle successful communication
    }
  } catch (error) {
    console.error('Iframe communication error:', error)
  }
})
```

## Performance Considerations

### Lazy Loading
Micro frontends are loaded on-demand when routes are accessed.

### Memory Management
Proper cleanup of iframe handshakes:
```javascript
beforeDestroy() {
  this.handshake.then((child) => {
    child.destroy()
  })
}
```

### Token Caching
Firebase tokens are cached and refreshed as needed to minimize authentication overhead.

## Development vs Production Differences

The configuration system supports different URLs for development and production environments, allowing local development of micro frontends while maintaining production stability.

Development typically uses localhost URLs while production uses hosted Firebase or custom domain URLs.
